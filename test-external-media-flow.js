/**
 * Test script để kiểm tra luồng External Media hoàn chỉnh
 */

const http = require('http');
const https = require('https');

// Helper function để thực hiện HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const protocol = options.protocol === 'https:' ? https : http;
    const req = protocol.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            data: body ? JSON.parse(body) : null
          };
          resolve(result);
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testExternalMediaFlow() {
  console.log('🧪 Testing External Media Flow...\n');

  try {
    // 1. Kiểm tra Audio Stream service
    console.log('1️⃣ Testing Audio Stream service...');
    const audioStreamResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/audioStream/startAudioStream',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      callSessionId: 'test-session-123',
      channelId: 'test-channel-456'
    });

    console.log('✅ Audio Stream response:', audioStreamResponse.data);

    // 2. Kiểm tra ARI Handler stats
    console.log('\n2️⃣ Testing ARI Handler stats...');
    const ariStatsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ariHandler/stats',
      method: 'GET'
    });
    console.log('✅ ARI Handler stats:', ariStatsResponse.data);

    // 3. Test External Media API trực tiếp
    console.log('\n3️⃣ Testing External Media API...');
    const auth = Buffer.from('ariuser:hE8CNBPi9p98Dv9Q').toString('base64');
    const externalMediaTest = await makeRequest({
      hostname: '***************',
      port: 8088,
      path: '/ari/channels/externalMedia',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${auth}`
      }
    }, {
      app: 'voicebot',
      external_host: '127.0.0.1:60000',
      format: 'ulaw'
    });

    console.log('✅ External Media channel created:', externalMediaTest.data);

    // 4. Cleanup - xóa channel test
    if (externalMediaTest.data && externalMediaTest.data.id) {
      console.log('\n4️⃣ Cleaning up test channel...');
      await makeRequest({
        hostname: '***************',
        port: 8088,
        path: `/ari/channels/${externalMediaTest.data.id}`,
        method: 'DELETE',
        headers: {
          'Authorization': `Basic ${auth}`
        }
      });
      console.log('✅ Test channel cleaned up');
    }

    console.log('\n🎉 All tests passed! External Media flow is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error details:', error);
  }
}

// Chạy test
testExternalMediaFlow();
