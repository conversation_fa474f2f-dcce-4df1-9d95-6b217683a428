"use strict";

const AriClient = require("ari-client");
const fs = require("fs");
const path = require("path");

/**
 * AI-powered Call Handler Service
 * Handles incoming calls with automatic AI responses using Whisper and TTS
 */
module.exports = {
  name: "ariHandler",

  /**
   * Service settings
   */
  settings: {
    // ARI connection settings
    ariUrl: process.env.ARI_URL || "http://***************:8088",
    ariUsername: process.env.ARI_USERNAME || "ariuser",
    ariPassword: process.env.ARI_PASSWORD || "hE8CNBPi9p98Dv9Q",
    ariApp: process.env.ARI_APP || "voicebot",

    // Call handling settings
    maxCallDuration: 5 * 60 * 1000, // 5 minutes
    recordingTimeout: 30 * 1000, // 30 seconds
    silenceTimeout: 5 * 1000, // 5 seconds
    maxConversationTurns: 10,

    // AI settings
    defaultGreeting: "Xin chào! Tôi là trợ lý AI của GHVN. Tôi có thể giúp gì cho bạn?",
    endCallMessage: "Cảm ơn bạn đã gọi. <PERSON><PERSON><PERSON> bạn một ngày tốt lành!",

    // Storage settings
    recordingsPath: path.join(__dirname, "recordings"),
  },

  /**
   * Service dependencies
   */
  dependencies: ["whisper", "tts", "langchain", "ssh", "audioStream"],

  /**
   * Service actions
   */
  actions: {
    /**
     * Get call statistics
     */
    getStats: {
      rest: "GET /stats",
      async handler(ctx) {
        return {
          activeCalls: this.activeCalls.size,
          totalCalls: this.totalCalls,
          successfulCalls: this.successfulCalls,
          failedCalls: this.failedCalls
        };
      }
    },

    /**
     * End a specific call
     */
    endCall: {
      rest: "POST /end-call",
      params: {
        channelId: "string"
      },
      async handler(ctx) {
        const {channelId} = ctx.params;
        const callSession = this.activeCalls.get(channelId);

        if (callSession) {
          await this.endCall(callSession);
          return {success: true, message: "Call ended successfully"};
        }

        return {success: false, message: "Call not found"};
      }
    }
  },

  /**
   * Service events
   */
  events: {
    /**
     * Handle transcription events from audio stream service
     */
    "audioStream.transcription"(payload) {
      this.handleStreamTranscription(payload);
    }
  },

  /**
   * Service methods
   */
  methods: {
    /**
     * Initialize ARI client and start listening
     */
    async initializeARI() {
      try {
        this.ariClient = await AriClient.connect(
          this.settings.ariUrl,
          this.settings.ariUsername,
          this.settings.ariPassword
        );

        this.logger.info("✅ Connected to Asterisk ARI", {
          url: this.settings.ariUrl,
          app: this.settings.ariApp
        });

        // Set up event handlers
        this.setupEventHandlers();

        // Start the ARI application
        await this.ariClient.start(this.settings.ariApp);

        this.logger.info(`🚀 ARI application '${this.settings.ariApp}' started`);

      } catch (error) {
        this.logger.error("❌ Failed to connect to ARI:", error);
        throw error;
      }
    },

    /**
     * Set up ARI event handlers
     */
    setupEventHandlers() {
      // Handle new calls entering Stasis
      this.ariClient.on("StasisStart", this.handleStasisStart.bind(this));

      // Handle calls leaving Stasis
      this.ariClient.on("StasisEnd", this.handleStasisEnd.bind(this));

      // Handle channel hangups
      this.ariClient.on("ChannelHangupRequest", this.handleChannelHangup.bind(this));

      // Handle DTMF events
      this.ariClient.on("ChannelDtmfReceived", this.handleDtmf.bind(this));

      // Handle recording events
      this.ariClient.on("RecordingStarted", this.handleRecordingStarted.bind(this));
      this.ariClient.on("RecordingFinished", this.handleRecordingFinished.bind(this));
      this.ariClient.on("RecordingFailed", this.handleRecordingFailed.bind(this));

      // Handle playback events
      this.ariClient.on("PlaybackStarted", this.handlePlaybackStarted.bind(this));
      this.ariClient.on("PlaybackFinished", this.handlePlaybackFinished.bind(this));

      this.logger.info("📡 ARI event handlers configured");
    },

    /**
     * Handle new call entering Stasis application
     */
    async handleStasisStart(event, channel) {
      const caller = channel.caller.number || "Unknown";
      const channelId = channel.id;

      this.logger.info(`📞 Incoming call from ${caller}`, {
        channelId,
        caller,
        timestamp: new Date().toISOString()
      });

      try {
        // Answer the call
        await channel.answer();
        this.logger.info(`✅ Call answered`, {channelId, caller});

        // Create call session
        const callSession = this.createCallSession(channel, caller);
        this.activeCalls.set(channelId, callSession);
        this.totalCalls++;

        // Start the conversation
        await this.startConversation(callSession);

      } catch (error) {
        this.logger.error(`❌ Error handling incoming call:`, error.message, {channelId, caller});
        this.failedCalls++;

        try {
          // Only try to hangup if the channel still exists
          if (!error.message || !error.message.includes('not found')) {
            await channel.hangup();
          }
        } catch (hangupError) {
          this.logger.error(`❌ Error hanging up channel:`, hangupError.message, {channelId});
        }
      }
    },

    /**
     * Handle call leaving Stasis application
     */
    async handleStasisEnd(event, channel) {
      const channelId = channel.id;
      const callSession = this.activeCalls.get(channelId);

      if (callSession) {
        this.logger.info(`📴 Call ended`, {
          channelId,
          caller: callSession.caller,
          duration: Date.now() - callSession.startTime,
          turns: callSession.conversationTurns
        });

        await this.cleanupCallSession(callSession);
        this.activeCalls.delete(channelId);
      }
    },

    /**
     * Handle channel hangup
     */
    async handleChannelHangup(event, channel) {
      const channelId = channel.id;
      const callSession = this.activeCalls.get(channelId);

      if (callSession) {
        this.logger.info(`📞 Channel hangup requested`, {channelId, caller: callSession.caller});
        callSession.isEnding = true;

        // Stop any ongoing recordings
        if (callSession.currentRecording) {
          try {
            await callSession.currentRecording.stop();
          } catch (error) {
            this.logger.warn(`⚠️ Error stopping recording:`, error, {channelId});
          }
        }

        // Stop any ongoing playbacks
        if (callSession.currentPlayback) {
          try {
            await callSession.currentPlayback.stop();
          } catch (error) {
            this.logger.warn(`⚠️ Error stopping playback:`, error, {channelId});
          }
        }
      }
    },

    /**
     * Handle DTMF input
     */
    async handleDtmf(event, channel) {
      const channelId = channel.id;
      const digit = event.digit;
      const callSession = this.activeCalls.get(channelId);

      if (!callSession) return;

      this.logger.info(`🔢 DTMF received: ${digit}`, {channelId, caller: callSession.caller});

      // Handle special DTMF commands
      switch (digit) {
        case '#':
          // End current recording and process
          if (callSession.currentRecording && callSession.state === 'recording') {
            await this.stopRecordingAndProcess(callSession);
          }
          break;

        case '*':
          // Cancel current operation
          if (callSession.currentRecording) {
            await this.cancelRecording(callSession);
          } else if (callSession.currentPlayback) {
            await this.stopPlayback(callSession);
          }
          break;

        case '0':
          // Transfer to human agent (future implementation)
          await this.transferToAgent(callSession);
          break;

        case '9':
          // End call
          await this.endCall(callSession);
          break;
      }
    },

    /**
     * Handle recording started
     */
    async handleRecordingStarted(event, recording) {
      const recordingName = recording.name;
      this.logger.info(`🎙️ Recording started: ${recordingName}`);

      // Find call session by recording name
      for (const [channelId, callSession] of this.activeCalls) {
        if (callSession.currentRecording && callSession.currentRecording.name === recordingName) {
          callSession.state = 'recording';

          // Set recording timeout
          callSession.recordingTimeout = setTimeout(async () => {
            this.logger.info(`⏰ Recording timeout reached`, {channelId, recordingName});
            await this.stopRecordingAndProcess(callSession);
          }, this.settings.recordingTimeout);

          break;
        }
      }
    },

    /**
     * Handle recording finished (legacy - now using real-time External Media)
     */
    async handleRecordingFinished(event, recording) {
      const recordingName = recording.name;
      this.logger.info(`✅ Recording finished: ${recordingName} (legacy mode)`);

      // Find call session
      for (const [channelId, callSession] of this.activeCalls) {
        if (callSession.currentRecording && callSession.currentRecording.name === recordingName) {

          // Clear recording timeout
          if (callSession.recordingTimeout) {
            clearTimeout(callSession.recordingTimeout);
            callSession.recordingTimeout = null;
          }

          callSession.currentRecording = null;
          callSession.state = 'processing';

          // For real-time processing, we don't need to process recordings
          // Audio is already being processed via External Media stream
          this.logger.info(`🔄 Real-time processing active, skipping recording processing`, {
            channelId: callSession.channelId,
            audioStreamActive: callSession.audioStreamActive
          });

          if (!callSession.audioStreamActive) {
            // Fallback to recording processing if External Media is not active
            await this.processRecording(callSession, recordingName);
          }
          break;
        }
      }
    },

    /**
     * Handle recording failed
     */
    async handleRecordingFailed(event, recording) {
      const recordingName = recording.name;
      this.logger.error(`❌ Recording failed: ${recordingName}`);

      // Find call session and handle the failure
      for (const [channelId, callSession] of this.activeCalls) {
        if (callSession.currentRecording && callSession.currentRecording.name === recordingName) {

          // Clear recording timeout
          if (callSession.recordingTimeout) {
            clearTimeout(callSession.recordingTimeout);
            callSession.recordingTimeout = null;
          }

          callSession.currentRecording = null;
          callSession.state = 'error';

          // Play error message and retry or end call
          await this.handleRecordingError(callSession);
          break;
        }
      }
    },

    /**
     * Handle playback started
     */
    async handlePlaybackStarted(event, playback) {
      const playbackId = playback.id;
      this.logger.info(`🔊 Playback started: ${playbackId}`);
    },

    /**
     * Handle playback finished
     */
    async handlePlaybackFinished(event, playback) {
      const playbackId = playback.id;
      this.logger.info(`✅ Playback finished: ${playbackId}`);

      // Find call session and continue conversation
      for (const [channelId, callSession] of this.activeCalls) {
        if (callSession.currentPlayback && callSession.currentPlayback.id === playbackId) {
          callSession.currentPlayback = null;
          callSession.state = 'waiting';

          // Start listening for user response
          await this.startListening(callSession);
          break;
        }
      }
    },

    /**
     * Create a new call session
     */
    createCallSession(channel, caller) {
      const channelId = channel.id;
      const sessionId = `call_${channelId}_${Date.now()}`;

      return {
        sessionId,
        channelId,
        channel,
        caller,
        startTime: Date.now(),
        state: 'starting', // starting, greeting, waiting, recording, processing, responding, ending
        conversationTurns: 0,
        conversationHistory: [],
        currentRecording: null,
        currentPlayback: null,
        recordingTimeout: null,
        isEnding: false,
        lastActivity: Date.now()
      };
    },

    /**
     * Start conversation with greeting
     */
    async startConversation(callSession) {
      try {
        callSession.state = 'greeting';

        this.logger.info(`🎯 Starting conversation`, {
          channelId: callSession.channelId,
          caller: callSession.caller
        });

        // Generate greeting audio using TTS
        const greetingText = this.settings.defaultGreeting;
        const audioResult = await this.generateAudio(greetingText);

        if (audioResult.success) {
          // Play greeting using the audio result object
          await this.playAudio(callSession, audioResult);
        } else {
          // Fallback to text-based greeting
          this.logger.warn(`⚠️ TTS failed, using fallback`, {
            channelId: callSession.channelId,
            error: audioResult.error
          });

          // Play a simple beep and start listening
          await this.playBeep(callSession);
          await this.startListening(callSession);
        }

      } catch (error) {
        this.logger.error(`❌ Error starting conversation:`, error, {
          channelId: callSession.channelId
        });
        await this.endCall(callSession);
      }
    },

    /**
     * Start listening for user input
     */
    async startListening(callSession) {
      if (callSession.isEnding) return;

      try {
        callSession.state = 'waiting';

        this.logger.info(`👂 Starting to listen`, {
          channelId: callSession.channelId,
          turn: callSession.conversationTurns + 1
        });

        // Check conversation limits
        if (callSession.conversationTurns >= this.settings.maxConversationTurns) {
          this.logger.info(`📊 Max conversation turns reached`, {
            channelId: callSession.channelId,
            turns: callSession.conversationTurns
          });
          await this.endCall(callSession);
          return;
        }

        // Start real-time audio streaming instead of recording
        this.logger.info(`🔍 About to start External Media stream`, {
          channelId: callSession.channelId
        });

        // Call the method directly with proper error handling
        try {
          await this.startExternalMediaStream(callSession);
        } catch (methodError) {
          this.logger.error(`❌ startExternalMediaStream failed:`, methodError);

          // Check if method exists
          if (typeof this.startExternalMediaStream !== 'function') {
            this.logger.error(`❌ Method startExternalMediaStream does not exist!`);
            // Fallback to legacy recording
            await this.startRecording(callSession);
          } else {
            throw methodError;
          }
        }

      } catch (error) {
        this.logger.error(`❌ Error starting to listen:`, error, {
          channelId: callSession.channelId
        });
        await this.endCall(callSession);
      }
    },

    /**
     * Start External Media stream for real-time audio processing
     */
    async startExternalMediaStream(callSession) {
      try {
        this.logger.info(`🌐 Starting External Media stream`, {
          channelId: callSession.channelId
        });

        // Check if ARI client is available
        if (!this.ariClient) {
          throw new Error("ARI client not initialized");
        }

        // Check if External Media API is available
        if (typeof this.ariClient.channels.externalMedia !== 'function') {
          throw new Error("External Media API not available in this Asterisk version");
        }

        // Start audio stream service
        const streamResult = await this.broker.call("audioStream.startAudioStream", {
          callSessionId: callSession.sessionId,
          channelId: callSession.channelId
        });

        if (!streamResult.success) {
          throw new Error(`Failed to start audio stream: ${streamResult.error}`);
        }

        // Create External Media channel
        // Use localhost IP instead of 0.0.0.0 for Asterisk to connect
        const audioStreamHost = streamResult.udpHost === '0.0.0.0' ? '127.0.0.1' : streamResult.udpHost;
        const externalMediaParams = {
          app: this.settings.ariApp,
          external_host: `${audioStreamHost}:${streamResult.udpPort}`,
          format: streamResult.format
        };

        this.logger.info(`🌐 Creating External Media channel with params:`, externalMediaParams);

        const externalMediaChannel = await this.ariClient.channels.externalMedia(externalMediaParams);

        this.logger.info(`🌐 External Media channel created`, {
          channelId: callSession.channelId,
          externalChannelId: externalMediaChannel.id,
          udpEndpoint: `${streamResult.udpHost}:${streamResult.udpPort}`
        });

        // Store external media channel reference
        callSession.externalMediaChannel = externalMediaChannel;
        callSession.audioStreamActive = true;

        // Add external media channel to the bridge
        if (callSession.bridge) {
          await callSession.bridge.addChannel({
            channel: externalMediaChannel.id
          });

          this.logger.info(`🌉 External Media channel added to bridge`, {
            channelId: callSession.channelId,
            bridgeId: callSession.bridge.id
          });
        }

        // Listen for transcription events from audio stream service
        // Note: Event subscription is handled in the service events section

        // Set timeout for user input
        callSession.streamTimeout = setTimeout(() => {
          this.handleStreamTimeout(callSession);
        }, this.settings.recordingTimeout);

        callSession.state = 'streaming';

      } catch (error) {
        this.logger.error(`❌ Failed to start External Media stream:`, error);
        await this.handleStreamingError(callSession);
      }
    },

    /**
     * Start recording user audio (legacy method - kept for fallback)
     */
    async startRecording(callSession) {
      try {
        const recordingName = `${callSession.sessionId}_turn_${callSession.conversationTurns + 1}_${Date.now()}`;
        const recordingPath = path.join(this.settings.recordingsPath, `${recordingName}.wav`);

        this.logger.info(`🎙️ Starting recording`, {
          channelId: callSession.channelId,
          recordingName,
          recordingPath
        });

        // Start recording
        const recording = await callSession.channel.record({
          name: recordingName,
          format: 'wav',
          maxDurationSeconds: 30,
          maxSilenceSeconds: 3,
          ifExists: 'overwrite',
          beep: false,
          terminateOn: '#'
        });

        callSession.currentRecording = recording;
        callSession.lastActivity = Date.now();

      } catch (error) {
        this.logger.error(`❌ Error starting recording:`, error, {
          channelId: callSession.channelId
        });
        await this.handleRecordingError(callSession);
      }
    },

    /**
     * Stop recording and process the audio
     */
    async stopRecordingAndProcess(callSession) {
      if (!callSession.currentRecording) return;

      try {
        this.logger.info(`⏹️ Stopping recording`, {
          channelId: callSession.channelId,
          recordingName: callSession.currentRecording.name
        });

        await callSession.currentRecording.stop();

      } catch (error) {
        this.logger.error(`❌ Error stopping recording:`, error, {
          channelId: callSession.channelId
        });
        await this.handleRecordingError(callSession);
      }
    },

    /**
     * Cancel current recording
     */
    async cancelRecording(callSession) {
      if (!callSession.currentRecording) return;

      try {
        this.logger.info(`❌ Cancelling recording`, {
          channelId: callSession.channelId,
          recordingName: callSession.currentRecording.name
        });

        await callSession.currentRecording.cancel();
        callSession.currentRecording = null;

        // Clear recording timeout
        if (callSession.recordingTimeout) {
          clearTimeout(callSession.recordingTimeout);
          callSession.recordingTimeout = null;
        }

        // Restart listening
        await this.startListening(callSession);

      } catch (error) {
        this.logger.error(`❌ Error cancelling recording:`, error, {
          channelId: callSession.channelId
        });
        await this.handleRecordingError(callSession);
      }
    },

    /**
     * Process recorded audio with AI
     */
    async processRecording(callSession, recordingName) {
      try {
        callSession.state = 'processing';

        this.logger.info(`🔄 Processing recording`, {
          channelId: callSession.channelId,
          recordingName
        });

        // Copy recording file from Asterisk server to local
        const remoteRecordingPath = `/var/spool/asterisk/recording/${recordingName}.wav`;
        const localRecordingPath = path.join(this.settings.recordingsPath, `${recordingName}.wav`);

        this.logger.info(`📥 Copying recording from Asterisk server`, {
          channelId: callSession.channelId,
          remoteFile: remoteRecordingPath,
          localFile: localRecordingPath
        });

        // Check if we're using real-time External Media processing
        if (callSession.audioStreamActive) {
          this.logger.info(`🔄 Using real-time External Media, skipping file copy`, {
            channelId: callSession.channelId
          });
          // For real-time processing, we don't need to copy files
          // Audio is already being processed via External Media stream
          await this.startListening(callSession);
          return;
        }

        // Fallback: Copy file from remote server via SSH (legacy mode)
        let recordingPath = localRecordingPath;
        try {
          const copyResult = await this.copyRecordingFromAsterisk(remoteRecordingPath, localRecordingPath);
          if (!copyResult.success) {
            throw new Error(`SSH copy failed: ${copyResult.error}`);
          }
          this.logger.info(`✅ Recording copied successfully (legacy mode)`, {
            channelId: callSession.channelId,
            fileSize: copyResult.fileSize
          });
        } catch (copyError) {
          this.logger.error(`❌ Failed to copy recording from server:`, copyError, {
            channelId: callSession.channelId
          });
          await this.handleRecordingError(callSession);
          return;
        }

        // Check if local file exists
        if (!fs.existsSync(recordingPath)) {
          this.logger.error(`❌ Local recording file not found: ${recordingPath}`, {
            channelId: callSession.channelId
          });
          await this.handleRecordingError(callSession);
          return;
        }

        // Transcribe audio using Whisper
        const transcriptionResult = await this.transcribeAudio(recordingPath);

        if (!transcriptionResult.success) {
          this.logger.error(`❌ Transcription failed`, {
            channelId: callSession.channelId,
            error: transcriptionResult.error
          });
          await this.handleRecordingError(callSession);
          return;
        }

        const userText = transcriptionResult.text;
        this.logger.info(`📝 User said: "${userText}"`, {
          channelId: callSession.channelId
        });

        // Add to conversation history
        callSession.conversationHistory.push({
          role: 'user',
          content: userText,
          timestamp: new Date().toISOString()
        });

        // Generate AI response
        const aiResponse = await this.generateAIResponse(callSession, userText);

        if (!aiResponse.success) {
          this.logger.error(`❌ AI response generation failed`, {
            channelId: callSession.channelId,
            error: aiResponse.error
          });
          await this.handleAIError(callSession);
          return;
        }

        const responseText = aiResponse.text;
        this.logger.info(`🤖 AI response: "${responseText}"`, {
          channelId: callSession.channelId
        });

        // Add AI response to conversation history
        callSession.conversationHistory.push({
          role: 'assistant',
          content: responseText,
          timestamp: new Date().toISOString()
        });

        callSession.conversationTurns++;

        // Generate audio response
        const audioResult = await this.generateAudio(responseText);

        if (audioResult.success) {
          // Play AI response using the audio result object
          await this.playAudio(callSession, audioResult);
        } else {
          this.logger.error(`❌ TTS failed`, {
            channelId: callSession.channelId,
            error: audioResult.error
          });
          await this.handleTTSError(callSession);
        }

        // Clean up recording files (both local and remote)
        try {
          // Clean up local file
          fs.unlinkSync(recordingPath);
          this.logger.info(`🧹 Local recording file cleaned up: ${recordingPath}`);

          // Clean up remote file on Asterisk server
          try {
            await this.broker.call("ssh.cleanupRemoteFile", {
              remoteFilePath: remoteRecordingPath
            });
            this.logger.info(`🧹 Remote recording file cleaned up: ${remoteRecordingPath}`);
          } catch (remoteCleanupError) {
            this.logger.warn(`⚠️ Failed to cleanup remote recording file: ${remoteRecordingPath}`, remoteCleanupError);
          }
        } catch (cleanupError) {
          this.logger.warn(`⚠️ Failed to cleanup local recording file: ${recordingPath}`, cleanupError);
        }

      } catch (error) {
        this.logger.error(`❌ Error processing recording:`, error, {
          channelId: callSession.channelId
        });
        await this.handleRecordingError(callSession);
      }
    },

    /**
     * Transcribe audio using Whisper service
     */
    async transcribeAudio(audioPath) {
      try {
        const result = await this.broker.call("whisper.transcriptAudio", {
          audioPath: audioPath,
          model: "whisper-1"
        });

        if (result.error) {
          return {
            success: false,
            error: result.error
          };
        }

        return {
          success: true,
          text: result.text || "",
          confidence: result.confidence || 0.8
        };

      } catch (error) {
        this.logger.error("Whisper service error:", error);
        return {
          success: false,
          error: error.message
        };
      }
    },

    /**
     * Generate AI response using LangChain service
     */
    async generateAIResponse(callSession, userText) {
      try {
        // Build conversation context and messages
        const context = this.buildConversationContext(callSession, userText);

        // Prepare messages for ChatCompletion
        const messages = [];

        // System message with context and role
        messages.push({
          role: "system",
          content: `Bạn là trợ lý AI của GHVN (Giao Hàng Việt Nam), một công ty giao hàng.
Nhiệm vụ của bạn là hỗ trợ khách hàng qua điện thoại một cách thân thiện và chuyên nghiệp.

Nguyên tắc trả lời:
- Trả lời ngắn gọn, rõ ràng (tối đa 2-3 câu)
- Sử dụng giọng điệu thân thiện, lịch sự
- Tập trung vào việc giải quyết vấn đề của khách hàng
- Nếu không thể giải quyết, hướng dẫn khách hàng liên hệ nhân viên

Context cuộc gọi:
${context}`
        });

        // Add conversation history (last 5 exchanges to keep context manageable)
        const recentHistory = callSession.conversationHistory.slice(-10);
        recentHistory.forEach(entry => {
          messages.push({
            role: entry.role === 'user' ? 'user' : 'assistant',
            content: entry.content
          });
        });

        // Add current user message
        messages.push({
          role: "user",
          content: userText
        });

        this.logger.info(`🤖 Calling LangChain chatCompletion`, {
          channelId: callSession.channelId,
          messagesCount: messages.length,
          userText: userText.substring(0, 100)
        });

        // Call LangChain chatCompletion
        const result = await this.broker.call("langchain.chatCompletion", {
          messages: messages,
          model: "gpt-4o-mini",
          temperature: 0.7,
          modelInterface: "ChatOpenAI"
        });

        // Handle different response formats
        let responseText = "";
        if (typeof result === 'string') {
          responseText = result;
        } else if (result && result.content) {
          responseText = result.content;
        } else if (result && result.text) {
          responseText = result.text;
        } else {
          throw new Error("Invalid response format from LangChain service");
        }

        // Validate response
        if (!responseText || responseText.trim().length === 0) {
          throw new Error("Empty response from AI");
        }

        this.logger.info(`🤖 AI response generated`, {
          channelId: callSession.channelId,
          responseLength: responseText.length,
          response: responseText.substring(0, 100)
        });

        return {
          success: true,
          text: responseText.trim(),
          confidence: 0.8
        };

      } catch (error) {
        this.logger.error("LangChain service error:", error);
        return {
          success: false,
          error: error.message
        };
      }
    },

    /**
     * Generate audio using TTS service
     */
    async generateAudio(text) {
      try {
        this.logger.info(`🎵 Generating audio for text: "${text.substring(0, 50)}..."`);

        const result = await this.broker.call("tts.textToSpeech", {
          text: text,
          voice: "alloy",
          model: "tts-1",
          speed: 1.0,
          forAsterisk: true  // Request Asterisk-compatible format
        });

        this.logger.info(`🎵 TTS result:`, {
          success: result.success,
          hasFile: !!result.file,
          hasAsteriskPath: !!result.asteriskPath,
          hasSoundName: !!result.soundName
        });

        if (result.error || !result.success) {
          return {
            success: false,
            error: result.error || "TTS generation failed"
          };
        }

        return {
          success: true,
          filePath: result.asteriskPath,
          soundName: result.soundName,
          file: result.file,
          buffer: result.buffer
        };

      } catch (error) {
        this.logger.error("❌ TTS service error:", error);
        return {
          success: false,
          error: error.message
        };
      }
    },

    /**
     * Build conversation context for AI
     */
    buildConversationContext(callSession, currentMessage) {
      const context = {
        caller: callSession.caller,
        callDuration: Date.now() - callSession.startTime,
        conversationTurns: callSession.conversationTurns,
        timestamp: new Date().toISOString(),
        currentMessage: currentMessage
      };

      // Add recent conversation history (last 5 exchanges)
      const recentHistory = callSession.conversationHistory.slice(-10);

      let contextText = `Cuộc gọi từ số ${callSession.caller}. `;
      contextText += `Đây là lượt hội thoại thứ ${callSession.conversationTurns + 1}. `;

      if (recentHistory.length > 0) {
        contextText += "Lịch sử hội thoại gần đây:\n";
        recentHistory.forEach((entry, index) => {
          const role = entry.role === 'user' ? 'Khách hàng' : 'Trợ lý';
          contextText += `${role}: ${entry.content}\n`;
        });
      }

      contextText += `\nTin nhắn hiện tại từ khách hàng: ${currentMessage}`;

      return contextText;
    },

    /**
     * Play audio to the channel
     */
    async playAudio(callSession, audioSource) {
      if (callSession.isEnding) return;

      try {
        callSession.state = 'responding';

        this.logger.info(`🔊 Playing audio`, {
          channelId: callSession.channelId,
          audioSourceType: typeof audioSource,
          hasFilePath: !!(audioSource && audioSource.filePath),
          hasSoundName: !!(audioSource && audioSource.soundName)
        });

        // Wait a moment to ensure channel is ready
        await new Promise(resolve => setTimeout(resolve, 500));

        // Check channel state and validity
        try {
          const channelInfo = await callSession.channel.get();
          if (channelInfo.state !== 'Up') {
            this.logger.warn(`⚠️ Channel not in Up state: ${channelInfo.state}`, {
              channelId: callSession.channelId
            });
            // If channel is not up, don't try to play audio
            if (channelInfo.state === 'Down' || channelInfo.state === 'Destroyed') {
              this.logger.warn(`⚠️ Channel is down/destroyed, skipping audio playback`, {
                channelId: callSession.channelId,
                state: channelInfo.state
              });
              return;
            }
            // Wait a bit more for channel to be ready
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (channelError) {
          this.logger.warn(`⚠️ Could not get channel info, channel may be destroyed:`, channelError.message);
          // If we can't get channel info, the channel is likely destroyed
          if (channelError.message && channelError.message.includes('not found')) {
            this.logger.warn(`⚠️ Channel not found, skipping audio playback`, {
              channelId: callSession.channelId
            });
            return;
          }
        }

        let mediaUri;

        this.logger.info(`🔊 Audio source debug:`, {
          type: typeof audioSource,
          hasFilePath: !!(audioSource && audioSource.filePath),
          hasSoundName: !!(audioSource && audioSource.soundName),
          hasFile: !!(audioSource && audioSource.file),
          soundName: audioSource?.soundName,
          filePath: audioSource?.filePath
        });

        // Handle different audio source types
        if (typeof audioSource === 'string') {
          // Simple string path or sound name
          if (audioSource.startsWith('http')) {
            mediaUri = audioSource;
          } else if (audioSource.startsWith('/')) {
            // Absolute file path - use sound: prefix without file extension
            const pathWithoutExt = audioSource.replace(/\.[^/.]+$/, "");
            mediaUri = `sound:${pathWithoutExt}`;
          } else {
            // Sound name without extension
            mediaUri = `sound:${audioSource}`;
          }
        } else if (audioSource && audioSource.soundName) {
          // TTS result with sound name (preferred for Asterisk)
          // Check if SSH copy was successful and use remote path
          if (audioSource.sshCopy && audioSource.sshCopy.success) {
            // Use remote path - file is available on Asterisk server
            const remotePathWithoutExt = audioSource.sshCopy.remoteFilePath.replace(/\.[^/.]+$/, "");
            mediaUri = `sound:${remotePathWithoutExt}`;
            this.logger.info(`🔊 Using SSH copied file (remote): ${mediaUri}`);

            // Store alternative URIs for fallback
            callSession.alternativeMediaUris = [
              `sound:voicebot/${audioSource.soundName}`,
              `sound:${audioSource.soundName}`,
              `sound:/var/lib/asterisk/sounds/voicebot/${audioSource.soundName}`,
              audioSource.filePath
            ];
          } else {
            // Fallback to local/custom path
            mediaUri = `sound:voicebot/${audioSource.soundName}`;
            this.logger.info(`🔊 Using local sound name: ${mediaUri}`);

            // Store alternative URIs for fallback
            callSession.alternativeMediaUris = [
              `sound:${audioSource.soundName}`,
              `sound:/var/lib/asterisk/sounds/voicebot/${audioSource.soundName}`,
              audioSource.filePath
            ];
          }
        } else if (audioSource && audioSource.filePath) {
          // TTS result with file path - try multiple approaches
          const pathWithoutExt = audioSource.filePath.replace(/\.[^/.]+$/, "");
          const filename = pathWithoutExt.split('/').pop();

          // Primary URI: try absolute path without extension
          mediaUri = `sound:${pathWithoutExt}`;
          this.logger.info(`🔊 Using file path (absolute): ${mediaUri}`);

          // Store alternative URIs for fallback
          callSession.alternativeMediaUris = [
            `sound:voicebot/${filename}`,
            `sound:${filename}`,
            `sound:/var/lib/asterisk/sounds/voicebot/${filename}`,
            audioSource.filePath
          ];
        } else if (audioSource && audioSource.file && audioSource.file._id) {
          // Legacy file object handling
          mediaUri = `sound:/var/spool/asterisk/recording/${audioSource.file._id}`;
        } else {
          throw new Error(`Invalid audio source format: ${JSON.stringify(audioSource)}`);
        }

        this.logger.info(`🔊 Playing media URI: ${mediaUri}`, {
          channelId: callSession.channelId
        });

        // Try to play the audio with fallback mechanism
        let playback = null;
        let lastError = null;

        const urisToTry = [mediaUri, ...(callSession.alternativeMediaUris || [])];

        for (const uri of urisToTry) {
          try {
            this.logger.info(`🔊 Attempting playback with URI: ${uri}`);
            playback = await callSession.channel.play({
              media: uri
            });

            this.logger.info(`🔊 Playback started successfully with URI: ${uri}`, {
              channelId: callSession.channelId,
              playbackId: playback.id
            });

            break; // Success, exit loop

          } catch (playError) {
            this.logger.warn(`⚠️ Playback failed with URI: ${uri}`, playError.message);
            lastError = playError;
            playback = null;

            // If channel not found, stop trying other URIs
            if (playError.message && playError.message.includes('not found')) {
              this.logger.warn(`⚠️ Channel not found during playback, stopping attempts`, {
                channelId: callSession.channelId
              });
              break;
            }
          }
        }

        if (!playback) {
          throw lastError || new Error("All playback attempts failed");
        }

        callSession.currentPlayback = playback;
        callSession.lastActivity = Date.now();

      } catch (error) {
        this.logger.error(`❌ Error playing audio:`, error, {
          channelId: callSession.channelId,
          audioSource: audioSource
        });

        // Enhanced fallback: try built-in sounds first
        try {
          this.logger.info(`🔊 Trying fallback sounds...`);

          // Try some common Asterisk built-in sounds
          const fallbackSounds = ['beep', 'hello', 'welcome'];
          let fallbackWorked = false;

          for (const sound of fallbackSounds) {
            try {
              const fallbackPlayback = await callSession.channel.play({
                media: `sound:${sound}`
              });

              this.logger.info(`✅ Fallback sound worked: ${sound}`);
              callSession.currentPlayback = fallbackPlayback;
              fallbackWorked = true;
              break;
            } catch (soundError) {
              this.logger.warn(`⚠️ Fallback sound failed: ${sound}`, soundError.message);

              // If channel not found, stop trying fallback sounds
              if (soundError.message && soundError.message.includes('not found')) {
                this.logger.warn(`⚠️ Channel not found during fallback, stopping attempts`);
                break;
              }
            }
          }

          if (!fallbackWorked) {
            // Last resort: just continue without audio
            this.logger.warn(`⚠️ All audio fallbacks failed, continuing without audio`);
            await this.startListening(callSession);
          }

        } catch (fallbackError) {
          this.logger.error(`❌ All fallbacks failed:`, fallbackError);
          // Check if the error is due to channel not found
          if (fallbackError.message && fallbackError.message.includes('not found')) {
            this.logger.warn(`⚠️ Channel not found, cleaning up call session`);
            await this.cleanupCallSession(callSession);
            this.activeCalls.delete(callSession.channelId);
          } else {
            await this.endCall(callSession);
          }
        }
      }
    },

    /**
     * Play a simple beep sound
     */
    async playBeep(callSession) {
      try {
        await callSession.channel.play({
          media: "sound:beep"
        });
      } catch (error) {
        this.logger.warn(`⚠️ Error playing beep:`, error.message, {
          channelId: callSession.channelId
        });

        // If channel not found, clean up the call session
        if (error.message && error.message.includes('not found')) {
          this.logger.warn(`⚠️ Channel not found during beep, cleaning up call session`);
          await this.cleanupCallSession(callSession);
          this.activeCalls.delete(callSession.channelId);
        }
      }
    },

    /**
     * Stop current playback
     */
    async stopPlayback(callSession) {
      if (!callSession.currentPlayback) return;

      try {
        this.logger.info(`⏹️ Stopping playback`, {
          channelId: callSession.channelId,
          playbackId: callSession.currentPlayback.id
        });

        await callSession.currentPlayback.stop();
        callSession.currentPlayback = null;

      } catch (error) {
        this.logger.error(`❌ Error stopping playback:`, error, {
          channelId: callSession.channelId
        });
      }
    },

    /**
     * Handle recording errors
     */
    async handleRecordingError(callSession) {
      this.logger.error(`❌ Recording error occurred`, {
        channelId: callSession.channelId
      });

      try {
        // Play error message
        const errorMessage = "Xin lỗi, có lỗi xảy ra khi ghi âm. Vui lòng thử lại.";
        const audioResult = await this.generateAudio(errorMessage);

        if (audioResult.success) {
          await this.playAudio(callSession, audioResult);
        } else {
          await this.playBeep(callSession);
          await this.startListening(callSession);
        }

      } catch (error) {
        this.logger.error(`❌ Error handling recording error:`, error, {
          channelId: callSession.channelId
        });
        await this.endCall(callSession);
      }
    },

    /**
     * Handle AI response errors
     */
    async handleAIError(callSession) {
      this.logger.error(`❌ AI error occurred`, {
        channelId: callSession.channelId
      });

      try {
        // Play fallback message
        const fallbackMessage = "Xin lỗi, tôi không thể xử lý yêu cầu của bạn lúc này. Vui lòng thử lại hoặc liên hệ nhân viên hỗ trợ.";
        const audioResult = await this.generateAudio(fallbackMessage);

        if (audioResult.success) {
          await this.playAudio(callSession, audioResult);
        } else {
          await this.endCall(callSession);
        }

      } catch (error) {
        this.logger.error(`❌ Error handling AI error:`, error, {
          channelId: callSession.channelId
        });
        await this.endCall(callSession);
      }
    },

    /**
     * Handle TTS errors
     */
    async handleTTSError(callSession) {
      this.logger.error(`❌ TTS error occurred`, {
        channelId: callSession.channelId
      });

      try {
        // Play beep and continue listening
        await this.playBeep(callSession);
        await this.startListening(callSession);

      } catch (error) {
        this.logger.error(`❌ Error handling TTS error:`, error, {
          channelId: callSession.channelId
        });
        await this.endCall(callSession);
      }
    },

    /**
     * Transfer call to human agent (placeholder)
     */
    async transferToAgent(callSession) {
      this.logger.info(`📞 Transferring to agent`, {
        channelId: callSession.channelId
      });

      try {
        const transferMessage = "Đang chuyển cuộc gọi đến nhân viên hỗ trợ. Vui lòng chờ trong giây lát.";
        const audioResult = await this.generateAudio(transferMessage);

        if (audioResult.success) {
          await this.playAudio(callSession, audioResult);
        }

        // TODO: Implement actual transfer logic
        // For now, just end the call
        setTimeout(() => {
          this.endCall(callSession);
        }, 3000);

      } catch (error) {
        this.logger.error(`❌ Error transferring to agent:`, error, {
          channelId: callSession.channelId
        });
        await this.endCall(callSession);
      }
    },

    /**
     * End the call gracefully
     */
    async endCall(callSession) {
      if (callSession.isEnding) return;

      callSession.isEnding = true;
      callSession.state = 'ending';

      this.logger.info(`📴 Ending call`, {
        channelId: callSession.channelId,
        caller: callSession.caller,
        duration: Date.now() - callSession.startTime,
        turns: callSession.conversationTurns
      });

      try {
        // Play goodbye message
        const goodbyeMessage = this.settings.endCallMessage;
        const audioResult = await this.generateAudio(goodbyeMessage);

        if (audioResult.success) {
          // Play goodbye using the proper playAudio method
          await this.playAudio(callSession, audioResult);

          // Wait for playback to finish, then hangup
          setTimeout(async () => {
            try {
              await callSession.channel.hangup();
            } catch (hangupError) {
              this.logger.warn(`⚠️ Error hanging up:`, hangupError, {
                channelId: callSession.channelId
              });
            }
          }, 3000);
        } else {
          // Immediate hangup if TTS fails
          await callSession.channel.hangup();
        }

        this.successfulCalls++;

      } catch (error) {
        this.logger.error(`❌ Error ending call:`, error, {
          channelId: callSession.channelId
        });

        try {
          await callSession.channel.hangup();
        } catch (hangupError) {
          this.logger.error(`❌ Error hanging up channel:`, hangupError, {
            channelId: callSession.channelId
          });
        }
      }
    },

    /**
     * Handle transcription from audio stream
     */
    async handleStreamTranscription(data) {
      const { callSessionId, channelId, text, confidence } = data;

      try {
        // Find the call session
        const callSession = Array.from(this.activeCalls.values())
          .find(session => session.sessionId === callSessionId);

        if (!callSession) {
          this.logger.warn(`Call session not found for transcription`, { callSessionId });
          return;
        }

        if (callSession.state !== 'streaming') {
          this.logger.warn(`Call session not in streaming state`, {
            callSessionId,
            currentState: callSession.state
          });
          return;
        }

        this.logger.info(`📝 Received transcription from stream`, {
          channelId: callSession.channelId,
          text: text,
          confidence: confidence
        });

        // Clear stream timeout
        if (callSession.streamTimeout) {
          clearTimeout(callSession.streamTimeout);
          callSession.streamTimeout = null;
        }

        // Stop external media stream
        await this.stopExternalMediaStream(callSession);

        // Process the transcription like we would with recording
        await this.processTranscription(callSession, text, confidence);

      } catch (error) {
        this.logger.error(`❌ Error handling stream transcription:`, error);
      }
    },

    /**
     * Handle stream timeout
     */
    async handleStreamTimeout(callSession) {
      try {
        this.logger.info(`⏰ Stream timeout reached`, {
          channelId: callSession.channelId
        });

        // Stop external media stream
        await this.stopExternalMediaStream(callSession);

        // Play timeout message and continue
        const timeoutMessage = "Tôi không nghe thấy gì. Bạn có thể nói lại được không?";
        const audioResult = await this.generateAudio(timeoutMessage);

        if (audioResult.success) {
          await this.playAudio(callSession, audioResult);
        } else {
          // Continue listening if audio generation fails
          await this.startListening(callSession);
        }

      } catch (error) {
        this.logger.error(`❌ Error handling stream timeout:`, error);
        await this.endCall(callSession);
      }
    },

    /**
     * Stop External Media stream
     */
    async stopExternalMediaStream(callSession) {
      try {
        if (callSession.externalMediaChannel) {
          // Remove from bridge first
          if (callSession.bridge) {
            try {
              await callSession.bridge.removeChannel({
                channel: callSession.externalMediaChannel.id
              });
            } catch (removeError) {
              this.logger.warn(`⚠️ Failed to remove external media channel from bridge:`, removeError);
            }
          }

          // Hangup external media channel
          try {
            await callSession.externalMediaChannel.hangup();
          } catch (hangupError) {
            this.logger.warn(`⚠️ Failed to hangup external media channel:`, hangupError);
          }

          callSession.externalMediaChannel = null;
        }

        // Stop audio stream service
        if (callSession.audioStreamActive) {
          await this.broker.call("audioStream.stopAudioStream", {
            callSessionId: callSession.sessionId
          });
          callSession.audioStreamActive = false;
        }

        // Clear timeout
        if (callSession.streamTimeout) {
          clearTimeout(callSession.streamTimeout);
          callSession.streamTimeout = null;
        }

        callSession.state = 'waiting';

        this.logger.info(`🛑 External Media stream stopped`, {
          channelId: callSession.channelId
        });

      } catch (error) {
        this.logger.error(`❌ Error stopping External Media stream:`, error);
      }
    },

    /**
     * Handle streaming error
     */
    async handleStreamingError(callSession) {
      try {
        this.logger.error(`❌ Streaming error occurred`, {
          channelId: callSession.channelId
        });

        // Stop external media stream
        await this.stopExternalMediaStream(callSession);

        // Fallback to recording method
        this.logger.info(`🔄 Falling back to recording method`, {
          channelId: callSession.channelId
        });

        await this.startRecording(callSession);

      } catch (fallbackError) {
        this.logger.error(`❌ Fallback to recording also failed:`, fallbackError);
        await this.endCall(callSession);
      }
    },

    /**
     * Process transcription (common method for both streaming and recording)
     */
    async processTranscription(callSession, userText, confidence = 0.8) {
      try {
        this.logger.info(`🔄 Processing transcription`, {
          channelId: callSession.channelId,
          text: userText,
          confidence: confidence
        });

        // Validate transcription
        if (!userText || userText.trim().length === 0) {
          this.logger.warn(`⚠️ Empty transcription received`, {
            channelId: callSession.channelId
          });

          const clarificationMessage = "Xin lỗi, tôi không nghe rõ. Bạn có thể nói lại được không?";
          const audioResult = await this.generateAudio(clarificationMessage);

          if (audioResult.success) {
            await this.playAudio(callSession, audioResult);
          } else {
            await this.startListening(callSession);
          }
          return;
        }

        // Add user message to conversation history
        callSession.conversationHistory.push({
          role: 'user',
          content: userText.trim(),
          timestamp: new Date().toISOString(),
          confidence: confidence
        });

        // Generate AI response
        const aiResponse = await this.generateAIResponse(callSession, userText.trim());

        if (!aiResponse.success) {
          throw new Error(`AI response failed: ${aiResponse.error}`);
        }

        // Add AI response to conversation history
        callSession.conversationHistory.push({
          role: 'assistant',
          content: aiResponse.text,
          timestamp: new Date().toISOString()
        });

        // Increment conversation turn
        callSession.conversationTurns++;

        // Generate audio response
        const audioResult = await this.generateAudio(aiResponse.text);

        if (audioResult.success) {
          await this.playAudio(callSession, audioResult);
        } else {
          throw new Error(`Audio generation failed: ${audioResult.error}`);
        }

      } catch (error) {
        this.logger.error(`❌ Error processing transcription:`, error);
        await this.handleRecordingError(callSession);
      }
    },

    /**
     * Copy recording file from Asterisk server to local
     */
    async copyRecordingFromAsterisk(remoteFilePath, localFilePath) {
      try {
        this.logger.info(`📥 Copying recording file via SSH`, {
          remoteFile: remoteFilePath,
          localFile: localFilePath
        });

        // Use SSH service to download file from Asterisk server
        const result = await this.broker.call("ssh.downloadFileFromAsterisk", {
          remoteFilePath: remoteFilePath,
          localFilePath: localFilePath
        });

        if (result.success) {
          this.logger.info(`✅ Recording file copied successfully`, {
            localFile: localFilePath,
            fileSize: result.fileSize,
            transferTime: result.transferTime
          });

          return {
            success: true,
            localFilePath: localFilePath,
            fileSize: result.fileSize,
            transferTime: result.transferTime
          };
        } else {
          throw new Error(result.error || "SSH download failed");
        }

      } catch (error) {
        this.logger.error(`❌ Failed to copy recording file:`, error);
        return {
          success: false,
          error: error.message
        };
      }
    },

    /**
     * Clean up call session resources
     */
    async cleanupCallSession(callSession) {
      try {
        // Stop external media stream if active
        if (callSession.audioStreamActive || callSession.externalMediaChannel) {
          await this.stopExternalMediaStream(callSession);
        }

        // Clear any timeouts
        if (callSession.recordingTimeout) {
          clearTimeout(callSession.recordingTimeout);
          callSession.recordingTimeout = null;
        }

        if (callSession.streamTimeout) {
          clearTimeout(callSession.streamTimeout);
          callSession.streamTimeout = null;
        }

        // Stop any ongoing recordings
        if (callSession.currentRecording) {
          try {
            await callSession.currentRecording.cancel();
          } catch (error) {
            this.logger.warn(`⚠️ Error cancelling recording during cleanup:`, error);
          }
        }

        // Stop any ongoing playbacks
        if (callSession.currentPlayback) {
          try {
            await callSession.currentPlayback.stop();
          } catch (error) {
            this.logger.warn(`⚠️ Error stopping playback during cleanup:`, error);
          }
        }

        this.logger.info(`🧹 Call session cleaned up`, {
          channelId: callSession.channelId,
          sessionId: callSession.sessionId
        });

      } catch (error) {
        this.logger.error(`❌ Error cleaning up call session:`, error, {
          channelId: callSession.channelId
        });
      }
    },

    /**
     * Initialize storage directories
     */
    initializeStorage() {
      try {
        if (!fs.existsSync(this.settings.recordingsPath)) {
          fs.mkdirSync(this.settings.recordingsPath, {recursive: true});
          this.logger.info(`📁 Created recordings directory: ${this.settings.recordingsPath}`);
        }
      } catch (error) {
        this.logger.error(`❌ Error creating storage directories:`, error);
        throw error;
      }
    },

    /**
     * Start call monitoring and cleanup
     */
    startMonitoring() {
      // Monitor for stale calls and clean them up
      this.monitoringInterval = setInterval(() => {
        const now = Date.now();
        const staleThreshold = this.settings.maxCallDuration;

        for (const [channelId, callSession] of this.activeCalls) {
          const callDuration = now - callSession.startTime;
          const inactivityDuration = now - callSession.lastActivity;

          // End calls that exceed maximum duration
          if (callDuration > staleThreshold) {
            this.logger.warn(`⏰ Call exceeded maximum duration`, {
              channelId,
              duration: callDuration,
              maxDuration: staleThreshold
            });
            this.endCall(callSession);
          }

          // End calls that have been inactive for too long
          else if (inactivityDuration > 60000) { // 1 minute of inactivity
            this.logger.warn(`😴 Call inactive for too long`, {
              channelId,
              inactivity: inactivityDuration
            });
            this.endCall(callSession);
          }
        }
      }, 30000); // Check every 30 seconds

      this.logger.info(`👁️ Call monitoring started`);
    },

    /**
     * Stop call monitoring
     */
    stopMonitoring() {
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = null;
        this.logger.info(`👁️ Call monitoring stopped`);
      }
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
    // Initialize tracking variables
    this.activeCalls = new Map();
    this.totalCalls = 0;
    this.successfulCalls = 0;
    this.failedCalls = 0;
    this.ariClient = null;
    this.monitoringInterval = null;

    this.logger.info("🚀 ARI Handler service created");
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    try {
      // Initialize storage
      this.initializeStorage();

      // Initialize ARI connection
      await this.initializeARI();

      // Start monitoring
      this.startMonitoring();

      this.logger.info("✅ ARI Handler service started successfully");

    } catch (error) {
      this.logger.error("❌ Failed to start ARI Handler service:", error);
      throw error;
    }
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
    try {
      // Stop monitoring
      this.stopMonitoring();

      // End all active calls
      for (const [channelId, callSession] of this.activeCalls) {
        await this.cleanupCallSession(callSession);
      }
      this.activeCalls.clear();

      // Disconnect ARI client
      if (this.ariClient) {
        // Note: ari-client doesn't have a disconnect method,
        // but we can remove event listeners
        this.ariClient.removeAllListeners();
        this.ariClient = null;
      }

      this.logger.info("🛑 ARI Handler service stopped");

    } catch (error) {
      this.logger.error("❌ Error stopping ARI Handler service:", error);
    }
  }
};
