/**
 * Test script để kiểm tra luồng Real-time Audio Processing hoàn chỉnh
 */

const http = require('http');
const dgram = require('dgram');

// Helper function để thực hiện HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            data: body ? JSON.parse(body) : null
          };
          resolve(result);
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// Tạo fake RTP packets để test
function createFakeRTPPacket(sequenceNumber, timestamp) {
  const rtpHeader = Buffer.alloc(12);
  
  // RTP Version (2 bits) + Padding (1 bit) + Extension (1 bit) + CSRC count (4 bits)
  rtpHeader[0] = 0x80; // Version 2, no padding, no extension, no CSRC
  
  // Marker (1 bit) + Payload Type (7 bits) - ulaw = 0
  rtpHeader[1] = 0x00; // No marker, payload type 0 (ulaw)
  
  // Sequence number (16 bits)
  rtpHeader.writeUInt16BE(sequenceNumber, 2);
  
  // Timestamp (32 bits)
  rtpHeader.writeUInt32BE(timestamp, 4);
  
  // SSRC (32 bits)
  rtpHeader.writeUInt32BE(0x12345678, 8);
  
  // Fake audio payload (ulaw silence)
  const audioPayload = Buffer.alloc(160, 0xFF); // ulaw silence
  
  return Buffer.concat([rtpHeader, audioPayload]);
}

async function testRealTimeAudioFlow() {
  console.log('🧪 Testing Real-time Audio Processing Flow...\n');

  try {
    // 1. Tạo Audio Stream session
    console.log('1️⃣ Creating Audio Stream session...');
    const callSessionId = `test-session-${Date.now()}`;
    const channelId = `test-channel-${Date.now()}`;
    
    const audioStreamResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/audioStream/startAudioStream',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      callSessionId: callSessionId,
      channelId: channelId
    });
    
    console.log('✅ Audio Stream session created:', audioStreamResponse.data);

    // 2. Tạo External Media channel
    console.log('\n2️⃣ Creating External Media channel...');
    const auth = Buffer.from('ariuser:hE8CNBPi9p98Dv9Q').toString('base64');
    const externalMediaResponse = await makeRequest({
      hostname: '***************',
      port: 8088,
      path: '/ari/channels/externalMedia',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${auth}`
      }
    }, {
      app: 'voicebot',
      external_host: '127.0.0.1:60000',
      format: 'ulaw'
    });
    
    console.log('✅ External Media channel created:', externalMediaResponse.data);

    // 3. Gửi fake RTP packets để test
    console.log('\n3️⃣ Sending fake RTP packets...');
    const udpClient = dgram.createSocket('udp4');
    
    // Gửi 10 packets
    for (let i = 0; i < 10; i++) {
      const packet = createFakeRTPPacket(i + 1, i * 160);
      
      await new Promise((resolve) => {
        udpClient.send(packet, 60000, '127.0.0.1', (err) => {
          if (err) {
            console.error(`❌ Error sending packet ${i + 1}:`, err);
          } else {
            console.log(`📦 Sent RTP packet ${i + 1}`);
          }
          resolve();
        });
      });
      
      // Delay 20ms giữa các packets (50 packets/second)
      await new Promise(resolve => setTimeout(resolve, 20));
    }
    
    udpClient.close();
    console.log('✅ All RTP packets sent');

    // 4. Đợi một chút để xử lý
    console.log('\n4️⃣ Waiting for processing...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 5. Kiểm tra stats
    console.log('\n5️⃣ Checking ARI Handler stats...');
    const statsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ariHandler/stats',
      method: 'GET'
    });
    console.log('📊 ARI Handler stats:', statsResponse.data);

    // 6. Cleanup - xóa External Media channel
    if (externalMediaResponse.data && externalMediaResponse.data.id) {
      console.log('\n6️⃣ Cleaning up External Media channel...');
      await makeRequest({
        hostname: '***************',
        port: 8088,
        path: `/ari/channels/${externalMediaResponse.data.id}`,
        method: 'DELETE',
        headers: {
          'Authorization': `Basic ${auth}`
        }
      });
      console.log('✅ External Media channel cleaned up');
    }

    console.log('\n🎉 Real-time Audio Processing test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error details:', error);
  }
}

// Chạy test
testRealTimeAudioFlow();
