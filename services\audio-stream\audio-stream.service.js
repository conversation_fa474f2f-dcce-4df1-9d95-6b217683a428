"use strict";

const dgram = require('dgram');
const fs = require('fs');
const path = require('path');
const { Transform } = require('stream');

/**
 * Real-time Audio Streaming Service
 * Handles UDP/RTP audio streams from Asterisk External Media
 */
module.exports = {
  name: "audioStream",

  /**
   * Service settings
   */
  settings: {
    // UDP server settings
    udpPort: process.env.AUDIO_STREAM_PORT || 60000,
    udpHost: process.env.AUDIO_STREAM_HOST || "0.0.0.0",
    // External host for Asterisk to connect to (should be the actual IP of this machine)
    externalHost: process.env.AUDIO_STREAM_EXTERNAL_HOST || "************", // Backend machine IP
    
    // Audio processing settings
    audioFormat: "ulaw", // Asterisk default
    sampleRate: 8000,
    channels: 1,
    
    // Buffer settings
    bufferDuration: 3000, // 3 seconds of audio before processing
    silenceThreshold: 500, // 500ms of silence to trigger processing
    maxAudioDuration: 30000, // 30 seconds max
    
    // RTP settings
    rtpHeaderSize: 12,
    payloadType: 0, // PCMU/ulaw
  },

  /**
   * Service dependencies
   */
  dependencies: ["whisper", "langchain", "tts"],

  /**
   * Service actions
   */
  actions: {
    /**
     * Start audio streaming for a call session
     */
    startAudioStream: {
      params: {
        callSessionId: "string",
        channelId: "string"
      },
      async handler(ctx) {
        const { callSessionId, channelId } = ctx.params;
        
        try {
          this.logger.info(`🎙️ Starting audio stream for call session`, {
            callSessionId,
            channelId,
            udpPort: this.settings.udpPort
          });

          // Create audio session
          const audioSession = this.createAudioSession(callSessionId, channelId);
          this.activeSessions.set(callSessionId, audioSession);

          return {
            success: true,
            udpHost: this.settings.externalHost, // Use external host for Asterisk to connect
            udpPort: this.settings.udpPort,
            format: this.settings.audioFormat,
            sessionId: callSessionId
          };

        } catch (error) {
          this.logger.error(`❌ Failed to start audio stream:`, error);
          return {
            success: false,
            error: error.message
          };
        }
      }
    },

    /**
     * Stop audio streaming for a call session
     */
    stopAudioStream: {
      params: {
        callSessionId: "string"
      },
      async handler(ctx) {
        const { callSessionId } = ctx.params;
        
        try {
          const audioSession = this.activeSessions.get(callSessionId);
          if (audioSession) {
            await this.cleanupAudioSession(audioSession);
            this.activeSessions.delete(callSessionId);
            
            this.logger.info(`🛑 Audio stream stopped`, { callSessionId });
            return { success: true };
          }

          return { success: false, error: "Session not found" };

        } catch (error) {
          this.logger.error(`❌ Failed to stop audio stream:`, error);
          return {
            success: false,
            error: error.message
          };
        }
      }
    },

    /**
     * Get audio stream status
     */
    getStreamStatus: {
      params: {
        callSessionId: "string"
      },
      async handler(ctx) {
        const { callSessionId } = ctx.params;
        const audioSession = this.activeSessions.get(callSessionId);
        
        if (!audioSession) {
          return { exists: false };
        }

        return {
          exists: true,
          status: audioSession.status,
          packetsReceived: audioSession.packetsReceived,
          audioBufferSize: audioSession.audioBuffer.length,
          lastActivity: audioSession.lastActivity
        };
      }
    }
  },

  /**
   * Service methods
   */
  methods: {
    /**
     * Create audio session for a call
     */
    createAudioSession(callSessionId, channelId) {
      return {
        callSessionId,
        channelId,
        status: 'active',
        audioBuffer: Buffer.alloc(0),
        packetsReceived: 0,
        lastActivity: Date.now(),
        silenceStart: null,
        isProcessing: false,
        sequenceNumber: 0,
        timestamp: 0
      };
    },

    /**
     * Setup UDP server to receive audio streams
     */
    setupUDPServer() {
      this.udpServer = dgram.createSocket('udp4');

      this.udpServer.on('message', (msg, rinfo) => {
        this.handleAudioPacket(msg, rinfo);
      });

      this.udpServer.on('error', (err) => {
        this.logger.error('UDP server error:', err);
      });

      this.udpServer.bind(this.settings.udpPort, this.settings.udpHost, () => {
        this.logger.info(`🎙️ Audio stream server listening on ${this.settings.udpHost}:${this.settings.udpPort}`);
      });
    },

    /**
     * Handle incoming audio packet
     */
    async handleAudioPacket(packet, rinfo) {
      try {
        // Parse RTP header
        if (packet.length < this.settings.rtpHeaderSize) {
          return; // Invalid packet
        }

        const rtpHeader = this.parseRTPHeader(packet);
        const audioPayload = packet.slice(this.settings.rtpHeaderSize);

        // Log first few packets for debugging
        if (!this.firstPacketLogged) {
          this.logger.info(`🎵 [AUDIO PACKET] First RTP packet received from ${rinfo.address}:${rinfo.port}, size: ${packet.length} bytes`, {
            activeSessionsCount: this.activeSessions.size,
            activeSessions: Array.from(this.activeSessions.keys())
          });
          this.firstPacketLogged = true;
        }

        // Find corresponding audio session
        const audioSession = this.findSessionByRemoteInfo(rinfo);
        if (!audioSession) {
          this.logger.warn(`⚠️ [AUDIO PACKET] No audio session found for ${rinfo.address}:${rinfo.port} - creating new session`, {
            activeSessionsCount: this.activeSessions.size,
            activeSessions: Array.from(this.activeSessions.keys())
          });
          // Try to create a session for this remote endpoint
          const newSession = this.createAudioSession(`unknown-${Date.now()}`, `unknown-${Date.now()}`, rinfo);
          if (newSession) {
            this.logger.info(`✅ [AUDIO PACKET] Created new audio session for ${rinfo.address}:${rinfo.port}`);
          } else {
            return;
          }
        }

        // Update session activity
        audioSession.lastActivity = Date.now();
        audioSession.packetsReceived++;
        audioSession.sequenceNumber = rtpHeader.sequenceNumber;
        audioSession.timestamp = rtpHeader.timestamp;

        // Append audio data to buffer
        audioSession.audioBuffer = Buffer.concat([audioSession.audioBuffer, audioPayload]);

        // Check for silence detection
        const isSilent = this.detectSilence(audioPayload);
        
        if (isSilent) {
          if (!audioSession.silenceStart) {
            audioSession.silenceStart = Date.now();
          } else if (Date.now() - audioSession.silenceStart > this.settings.silenceThreshold) {
            // Silence detected, process audio
            await this.processAudioBuffer(audioSession);
          }
        } else {
          audioSession.silenceStart = null;
        }

        // Check buffer duration
        const bufferDuration = this.calculateBufferDuration(audioSession.audioBuffer);
        if (bufferDuration > this.settings.bufferDuration) {
          await this.processAudioBuffer(audioSession);
        }

        // Check max duration
        if (bufferDuration > this.settings.maxAudioDuration) {
          this.logger.warn(`Max audio duration reached, forcing processing`, {
            callSessionId: audioSession.callSessionId
          });
          await this.processAudioBuffer(audioSession);
        }

      } catch (error) {
        this.logger.error('Error handling audio packet:', error);
      }
    },

    /**
     * Parse RTP header
     */
    parseRTPHeader(packet) {
      const version = (packet[0] >> 6) & 0x03;
      const padding = (packet[0] >> 5) & 0x01;
      const extension = (packet[0] >> 4) & 0x01;
      const csrcCount = packet[0] & 0x0F;
      
      const marker = (packet[1] >> 7) & 0x01;
      const payloadType = packet[1] & 0x7F;
      
      const sequenceNumber = (packet[2] << 8) | packet[3];
      const timestamp = (packet[4] << 24) | (packet[5] << 16) | (packet[6] << 8) | packet[7];
      const ssrc = (packet[8] << 24) | (packet[9] << 16) | (packet[10] << 8) | packet[11];

      return {
        version,
        padding,
        extension,
        csrcCount,
        marker,
        payloadType,
        sequenceNumber,
        timestamp,
        ssrc
      };
    },

    /**
     * Find audio session by remote info
     */
    findSessionByRemoteInfo(rinfo) {
      // For now, return the first active session
      // In production, you'd map sessions to specific remote endpoints
      for (const session of this.activeSessions.values()) {
        if (session.status === 'active') {
          return session;
        }
      }
      return null;
    },

    /**
     * Detect silence in audio payload
     */
    detectSilence(audioPayload) {
      // Simple silence detection for ulaw
      // ulaw silence is typically around 0xFF (255) or 0x7F (127)
      let silentSamples = 0;
      const threshold = audioPayload.length * 0.8; // 80% silence

      for (let i = 0; i < audioPayload.length; i++) {
        const sample = audioPayload[i];
        // ulaw silence detection
        if (sample === 0xFF || sample === 0x7F || Math.abs(sample - 0xFF) < 10) {
          silentSamples++;
        }
      }

      return silentSamples > threshold;
    },

    /**
     * Calculate buffer duration in milliseconds
     */
    calculateBufferDuration(buffer) {
      // For ulaw: 8000 samples per second, 1 byte per sample
      const samplesPerMs = this.settings.sampleRate / 1000;
      return buffer.length / samplesPerMs;
    },

    /**
     * Process accumulated audio buffer
     */
    async processAudioBuffer(audioSession) {
      this.logger.info(`🎯 [AUDIO BUFFER] processAudioBuffer called`, {
        callSessionId: audioSession.callSessionId,
        isProcessing: audioSession.isProcessing,
        bufferLength: audioSession.audioBuffer.length,
        hasBuffer: audioSession.audioBuffer.length > 0
      });

      if (audioSession.isProcessing || audioSession.audioBuffer.length === 0) {
        this.logger.info(`⏭️ [AUDIO BUFFER] Skipping processing`, {
          callSessionId: audioSession.callSessionId,
          reason: audioSession.isProcessing ? 'already_processing' : 'empty_buffer',
          isProcessing: audioSession.isProcessing,
          bufferLength: audioSession.audioBuffer.length
        });
        return;
      }

      audioSession.isProcessing = true;

      try {
        this.logger.info(`🔄 [AUDIO BUFFER] Processing audio buffer`, {
          callSessionId: audioSession.callSessionId,
          bufferSize: audioSession.audioBuffer.length,
          duration: this.calculateBufferDuration(audioSession.audioBuffer)
        });

        // Convert ulaw to WAV for Whisper
        const wavBuffer = await this.convertUlawToWav(audioSession.audioBuffer);
        
        // Save temporary WAV file
        const tempWavPath = path.join(__dirname, 'temp', `${audioSession.callSessionId}_${Date.now()}.wav`);
        await this.ensureDirectoryExists(path.dirname(tempWavPath));
        await fs.promises.writeFile(tempWavPath, wavBuffer);

        // Transcribe with Whisper
        this.logger.info(`🎤 [TRANSCRIPTION] Calling Whisper service`, {
          callSessionId: audioSession.callSessionId,
          audioPath: tempWavPath,
          bufferSize: audioSession.audioBuffer.length
        });

        const transcriptionResult = await this.broker.call("whisper.transcriptAudio", {
          audioPath: tempWavPath,
          model: "whisper-1"
        });

        this.logger.info(`🎤 [TRANSCRIPTION] Whisper service response`, {
          callSessionId: audioSession.callSessionId,
          hasResult: !!transcriptionResult,
          hasText: !!(transcriptionResult && transcriptionResult.text),
          text: transcriptionResult?.text || 'NO_TEXT',
          confidence: transcriptionResult?.confidence || 'NO_CONFIDENCE'
        });

        if (transcriptionResult && transcriptionResult.text) {
          this.logger.info(`📝 [TRANSCRIPTION] Transcription successful: "${transcriptionResult.text}"`, {
            callSessionId: audioSession.callSessionId,
            textLength: transcriptionResult.text.length,
            confidence: transcriptionResult.confidence || 0.8
          });

          // Emit transcription event for ARI Handler
          this.logger.info(`📡 [TRANSCRIPTION] Emitting audioStream.transcription event`, {
            callSessionId: audioSession.callSessionId,
            channelId: audioSession.channelId,
            text: transcriptionResult.text
          });

          this.broker.emit("audioStream.transcription", {
            callSessionId: audioSession.callSessionId,
            channelId: audioSession.channelId,
            text: transcriptionResult.text,
            confidence: transcriptionResult.confidence || 0.8
          });

          this.logger.info(`✅ [TRANSCRIPTION] Event emitted successfully`, {
            callSessionId: audioSession.callSessionId
          });
        } else {
          this.logger.warn(`⚠️ [TRANSCRIPTION] No transcription result or empty text`, {
            callSessionId: audioSession.callSessionId,
            transcriptionResult: transcriptionResult
          });
        }

        // Cleanup temp file
        try {
          await fs.promises.unlink(tempWavPath);
        } catch (cleanupError) {
          this.logger.warn('Failed to cleanup temp file:', cleanupError);
        }

        // Clear audio buffer
        audioSession.audioBuffer = Buffer.alloc(0);
        audioSession.silenceStart = null;

      } catch (error) {
        this.logger.error('Error processing audio buffer:', error);
      } finally {
        audioSession.isProcessing = false;
      }
    },

    /**
     * Convert ulaw audio to WAV format
     */
    async convertUlawToWav(ulawBuffer) {
      // Simple ulaw to linear PCM conversion
      const pcmBuffer = Buffer.alloc(ulawBuffer.length * 2); // 16-bit samples
      
      for (let i = 0; i < ulawBuffer.length; i++) {
        const ulawSample = ulawBuffer[i];
        const linearSample = this.ulawToLinear(ulawSample);
        pcmBuffer.writeInt16LE(linearSample, i * 2);
      }

      // Create WAV header
      const wavHeader = this.createWavHeader(pcmBuffer.length);
      return Buffer.concat([wavHeader, pcmBuffer]);
    },

    /**
     * Convert ulaw sample to linear PCM
     */
    ulawToLinear(ulawSample) {
      // Standard ulaw to linear conversion
      const sign = (ulawSample & 0x80) ? -1 : 1;
      const exponent = (ulawSample >> 4) & 0x07;
      const mantissa = ulawSample & 0x0F;
      
      let sample = (mantissa << 3) + 0x84;
      sample <<= exponent;
      sample -= 0x84;
      
      return sign * sample;
    },

    /**
     * Create WAV file header
     */
    createWavHeader(dataSize) {
      const header = Buffer.alloc(44);
      
      // RIFF header
      header.write('RIFF', 0);
      header.writeUInt32LE(36 + dataSize, 4);
      header.write('WAVE', 8);
      
      // fmt chunk
      header.write('fmt ', 12);
      header.writeUInt32LE(16, 16); // chunk size
      header.writeUInt16LE(1, 20);  // PCM format
      header.writeUInt16LE(1, 22);  // mono
      header.writeUInt32LE(8000, 24); // sample rate
      header.writeUInt32LE(16000, 28); // byte rate
      header.writeUInt16LE(2, 32);  // block align
      header.writeUInt16LE(16, 34); // bits per sample
      
      // data chunk
      header.write('data', 36);
      header.writeUInt32LE(dataSize, 40);
      
      return header;
    },

    /**
     * Ensure directory exists
     */
    async ensureDirectoryExists(dirPath) {
      try {
        await fs.promises.access(dirPath);
      } catch {
        await fs.promises.mkdir(dirPath, { recursive: true });
      }
    },

    /**
     * Cleanup audio session
     */
    async cleanupAudioSession(audioSession) {
      audioSession.status = 'inactive';
      audioSession.audioBuffer = Buffer.alloc(0);
      
      this.logger.info(`🧹 Audio session cleaned up`, {
        callSessionId: audioSession.callSessionId,
        packetsReceived: audioSession.packetsReceived
      });
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
    this.activeSessions = new Map();
    this.firstPacketLogged = false;
    this.logger.info("🎙️ Audio Stream service created");
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    // Setup UDP server
    this.setupUDPServer();
    
    // Create temp directory
    const tempDir = path.join(__dirname, 'temp');
    await this.ensureDirectoryExists(tempDir);
    
    this.logger.info("🚀 Audio Stream service started");
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
    // Close UDP server
    if (this.udpServer) {
      this.udpServer.close();
    }

    // Cleanup all sessions
    for (const session of this.activeSessions.values()) {
      await this.cleanupAudioSession(session);
    }
    this.activeSessions.clear();

    this.logger.info("🛑 Audio Stream service stopped");
  }
};
