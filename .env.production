# Production Environment Configuration

# ===== AUDIO STREAMING CONFIGURATION =====
# External host that Asterisk can connect to (MUST be server's public/internal IP)
AUDIO_STREAM_EXTERNAL_HOST=<YOUR_PRODUCTION_SERVER_IP>
AUDIO_STREAM_HOST=0.0.0.0
AUDIO_STREAM_PORT=60000

# Audio processing settings
AUDIO_BUFFER_DURATION=3000
SILENCE_THRESHOLD=500
MAX_AUDIO_DURATION=30000

# ===== ASTERISK CONFIGURATION =====
# Asterisk server details (already configured)
ARI_URL=http://***************:8088
ARI_USERNAME=ariuser
ARI_PASSWORD=aripassword
ARI_APP=voicebot

# ===== SSH CONFIGURATION =====
# SSH to Asterisk server for TTS file copying
SSH_HOST=***************
SSH_USER=root
SSH_PASSWORD=Ey5WrsNDHynyEb4uawGO6hxWzIvOslTH
SSH_AUDIO_PATH=/var/lib/asterisk/sounds

# ===== DATABASE CONFIGURATION =====
# MongoDB connection for production
MONGO_URI=mongodb://localhost:27017/call-center-prod

# ===== API CONFIGURATION =====
# API server settings
PORT=3000
NODE_ENV=production

# ===== LOGGING CONFIGURATION =====
# Production logging level
LOG_LEVEL=info
LOG_FILE=/var/log/call-center/app.log

# ===== SECURITY CONFIGURATION =====
# JWT secrets for production
JWT_SECRET=<STRONG_JWT_SECRET_FOR_PRODUCTION>
JWT_REFRESH_SECRET=<STRONG_REFRESH_SECRET_FOR_PRODUCTION>

# ===== AI SERVICES CONFIGURATION =====
# OpenAI API key
OPENAI_API_KEY=<YOUR_OPENAI_API_KEY>

# Whisper service settings
WHISPER_MODEL=whisper-1
WHISPER_LANGUAGE=vi

# TTS service settings
TTS_VOICE=alloy
TTS_MODEL=tts-1
